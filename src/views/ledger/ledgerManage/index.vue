//工单台账管理
<template>
  <div class="card-container">
    <el-radio-group
      v-model="tabActiveTab"
      style="margin-bottom: 20px;"
      size="medium"
    >
      <el-radio-button
        :label="item.value"
        v-for="item in topTabDict"
        :key="item.value"
        >{{ item.label }}
      </el-radio-button>
    </el-radio-group>
    <BuseCrud
      ref="crud"
      :loading="loading"
      :filterOptions="filterOptions"
      :tablePage="tablePage"
      :pagerProps="pagerProps"
      :tableColumn="tableColumn"
      :tableData="tableData"
      :tableProps="tableProps"
      :modalConfig="modalConfig"
      @modalConfirm="modalConfirmHandler"
      @loadData="loadData"
      @rowDel="deleteRowHandler"
      selectStyleType="infoTip"
      @handleBatchSelect="handleBatchSelect"
      :showSelectNum="showSelectNum"
    >
      <!-- :tabRadioList="tabRadioList"
      @tabRadioChange="tabRadioChange"
      tabType="card" -->
      <template #toolbar_buttons>
        <div style="display: flex;justify-content: space-between;width: 100%;">
          <div>
            <el-button
              icon="el-icon-plus"
              type="primary"
              @click="JumpToEdit"
              v-has-permi="['ledger:list:add']"
              >新增</el-button
            >
            <el-button
              icon="el-icon-plus"
              type="primary"
              @click="handleBatchTypes"
              v-has-permi="['ledger:list:import']"
              >批量导入</el-button
            >
            <el-button
              type="primary"
              @click="handleBatchEnd"
              v-has-permi="['ledger:list:batchEnd']"
              >批量完结</el-button
            >
            <el-button
              type="primary"
              @click="handleSearchDrawer"
              v-has-permi="['ledger:list:formSearch']"
              >模糊搜索</el-button
            >
          </div>
          <el-button
            type="text"
            @click="handleCustom"
            style="margin-right: 10px;"
            :loading="btnLoading"
            >{{
              isCustomColumn ? "已开启偏好设置" : "将自定义列设为偏好"
            }}</el-button
          >
        </div>
      </template>
      <template #menu="{row,tableRow}">
        <el-button
          v-for="item in outBtnArr"
          :key="item.typeName"
          type="text"
          size="medium"
          @click="item.event(row)"
          v-show="!!item.condition(row)"
          >{{ item.title }}</el-button
        >
        <el-dropdown
          @command="(command) => handleCommand(command, row)"
          class="ml10"
        >
          <el-button type="text" size="medium">
            快捷操作<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item
              :command="item.typeName"
              v-for="(item, index) in btnArr"
              :key="index"
              v-show="!!item.condition(row)"
            >
              <el-button type="text" size="medium">
                {{ item.title }}
              </el-button>
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </template>
      <template slot="filterCustomBtn">
        <div class="btn-wrap">
          <el-button
            type="primary"
            icon="el-icon-download"
            @click.stop="handleExport"
            v-has-permi="['ledger:list:export']"
            >导出
          </el-button>
          <el-button
            type="primary"
            icon="el-icon-search"
            @click.stop="handleQuery"
            >查询
          </el-button>
          <el-button icon="el-icon-refresh" @click.stop="handleReset"
            >重置
          </el-button>
          <el-button
            type="text"
            @click="handleFilterCustom"
            style="margin-left: 10px;"
            :loading="filterBtnLoading"
            >{{ isFilterCustom ? "已开启偏好设置" : "设为偏好" }}</el-button
          >
          <FilterPreference
            v-model="selectedFilterFields"
            :filterOptions="filterOptions.config"
            :defaultFields="defaultFilterFields"
            :loading="filterBtnLoading"
            @change="handleFilterFieldsChange"
          />
        </div>
      </template>
      <template #withdraw="{ row, crudOperationType }">
        <div class="confirm-text">确定要撤回到草稿状态重新编辑吗？</div>
        <el-form :model="row" ref="withdrawForm" label-width="110px">
          <el-form-item label="原因" prop="reCallRemark">
            <el-input
              v-model="row.reCallRemark"
              rows="5"
              maxlength="500"
              show-word-limit
              type="textarea"
              placeholder="请输入具体的原因描述，500个字符以内"
            />
          </el-form-item>
        </el-form>
      </template>
      <template slot="orderProcess" slot-scope="{ row }">
        <el-progress :percentage="row.progress"></el-progress>
      </template>
      <template #deptTree="{item,params}">
        <treeselect
          v-model="params.deptId"
          :options="deptOptions"
          placeholder="请选择归属部门"
          @select="handleNodeClick"
          :beforeClearAll="beforeClearAll"
          :default-expand-level="1"
          :normalizer="normalizer"
        />
      </template>
      <!-- <template #modalFooter="{ row, crudOperationType }">
        <div v-if="crudOperationType === 'check'">
          <el-button @click="submitCheck(row, false)">审核不通过</el-button>
          <el-button @click="submitCheck(row, true)" type="primary"
            >审核通过</el-button
          >
        </div>
      </template> -->
    </BuseCrud>
    <BatchUploadTypes
      @uploadSuccess="loadData"
      ref="batchUploadTypes"
      :extraData="extraData"
    >
      <template #extraForm="{params}">
        <el-row>
          <el-col :span="24">
            <el-form-item label="数据处理方式:" prop="orderStatus">
              <el-radio-group v-model="params.orderStatus">
                <el-radio label="1">
                  处理中（开启流程，仅第一个节点默认已处理）
                </el-radio>
                <el-radio label="4">
                  完结（批量完结所有工单，将所有节点变成已处理）
                </el-radio>
                <el-radio label="0">
                  草稿（不开启流程，导入后工单状态为草稿）
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="完结原因:" prop="finishRemark">
              <el-input
                v-model="params.finishRemark"
                rows="5"
                maxlength="500"
                show-word-limit
                type="textarea"
                placeholder="如果选择完结，完结原因必填，500个字符以内"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </template>
    </BatchUploadTypes>
    <ProcessExceedDrawer ref="processExceedDrawer"></ProcessExceedDrawer>
    <ProcessDetailDrawer ref="processDetailDrawer"></ProcessDetailDrawer>
    <AddOrderDrawer ref="addOrderDrawer" @success="loadData"></AddOrderDrawer>
    <CheckOrderDrawer
      ref="checkOrderDrawer"
      @success="loadData"
    ></CheckOrderDrawer>
    <FormSearchDrawer
      ref="formSearchDrawer"
      @search="handleSearch"
    ></FormSearchDrawer>
    <CheckProcessDrawer ref="checkProcessDrawer"></CheckProcessDrawer>
  </div>
</template>

<script>
import BatchUploadTypes from "./components/batchUploadTypes.vue";
import checkPermission from "@/utils/permission.js";
import api from "@/api/ledger/index.js";
import { initParams } from "@/utils/buse.js";
import exportMixin from "@/mixin/export.js";
import { listAllUser, queryCityTree, listDept } from "@/api/common.js";
import { queryTreeList } from "@/api/ledger/businessType.js";
import { queryDeptOrderTree } from "@/api/ledger/workOrderType.js";
import ProcessExceedDrawer from "./components/processExceedDrawer.vue";
import ProcessDetailDrawer from "./components/processDetailDrawer.vue";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import comApi from "@/api/ledger/company.js";
import { regionData } from "element-china-area-data";
import { mapGetters } from "vuex";
import AddOrderDrawer from "./components/addOrderDrawer.vue";
import CheckOrderDrawer from "./components/checkOrderDrawer.vue";
import FormSearchDrawer from "./components/formSearchDrawer.vue";
import CheckProcessDrawer from "./components/checkProcessDrawer.vue";
import FilterPreference from "./components/FilterPreference.vue";
import moment from "moment";
export default {
  name: "ledgerList",
  components: {
    BatchUploadTypes,
    ProcessExceedDrawer,
    ProcessDetailDrawer,
    Treeselect,
    AddOrderDrawer,
    CheckOrderDrawer,
    FormSearchDrawer,
    CheckProcessDrawer,
    FilterPreference,
  },
  mixins: [exportMixin],
  data() {
    return {
      extraData: {
        finishRemark: "",
        orderStatus: "1",
      },
      storeData: {},
      filterStoreData: {},
      btnLoading: false,
      filterBtnLoading: false,
      restorePromise: null,
      filterRestorePromise: null,
      isCustomColumn: false,
      isFilterCustom: false,
      selectedFilterFields: [],
      defaultFilterFields: [
        "orderStatus",
        "orderNo",
        "businessType",
        "orderTypeArr",
        "supportDept",
        "handleUserName",
        "submitTime",
        "completeTime",
      ],
      tabActiveTab: "all",
      topTabDict: [
        { value: "all", label: "全部" },
        { value: "createUserFlag", label: "我创建的" },
        { value: "handleUserFlag", label: "我处理的" },
        { value: "transferUserFlag", label: "我转派的" },
      ],
      projectStatus: "",
      projectBatchId: "",
      businessTypeOptions: [],
      orderTypeOptions: [],
      activeTab: "0",
      //buse参数-s
      tabRadioList: [
        { value: "0", id: "0", label: "全部" },
        // { value: "1", id: "1", label: "进行中" },
      ],

      tableData: [],

      params: {},
      loading: false,
      pagerProps: {
        layouts: [
          "Total",
          "Sizes",
          "PrevPage",
          "JumpNumber",
          "NextPage",
          "FullJump",
        ],
        background: true,
        className: "pagination-container",
        pageSizes: [10, 20, 30, 50],
      },
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      },
      operationType: "transfer",
      //buse参数-e
      modalOrderTypeOptions: [],
      selectedData: [],
      statusOptions: [],
      supportDeptOptions: [],
      originOptions: [],
      urgencyDegreeOptions: [],
      deptOptions: [],
      userOption: [],
      groupOptions: [],
      roleOptions: [],
      actCityOptions: [],
      // 新增字典数据
      riskTypeOptions: [], // 风险类型
      riskLevelOptions: [], // 风险等级
      riskStatusOptions: [], // 风险状态
      bdNameOptions: [], // BD责任人
    };
  },

  watch: {
    tabActiveTab: {
      handler(val) {
        this.handleQuery();
      },
    },
    // "params.supportDept": {
    //   handler(val) {
    //     if (val) {
    //       this.getBusinessOptions(val);
    //     } else {
    //       this.orderTypeOptions = [];
    //       this.businessTypeOptions = [];
    //       this.urgencyDegreeOptions = [];
    //     }
    //   },
    // },
    // "params.businessType": {
    //   handler(val) {
    //     const len = val?.length || 0;
    //     if (len > 0) {
    //       api
    //         .queryOrderOptionsByBusiness({
    //           supportDept: this.params.supportDept,
    //           businessTypeId: val[len - 1],
    //         })
    //         .then((res) => {
    //           this.orderTypeOptions = res.data?.map((x) => {
    //             return { ...x, disabled: true };
    //           });
    //         });
    //       this.getUrgencyOptions({
    //         supportDept: this.params.supportDept,
    //         businessType: val[0],
    //       });
    //     } else {
    //       this.orderTypeOptions = [];
    //       this.urgencyDegreeOptions = [];
    //     }
    //   },
    // },
  },
  created() {
    this.params = {
      ...initParams(this.filterOptions.config),
      obscureParamsList: [{ name: undefined }],
    };

    this.selectedFilterFields = [...this.defaultFilterFields];

    // 初始化筛选条件偏好设置
    this.initFilterPreference();
    this.getActCityList();
    this.listAllUser();
    // this.getCityRegionData();
  },
  activated() {
    if (Object.keys(this.$route.params)?.length > 0) {
      this.params = { ...this.params, ...this.$route.params };
    }
    this.loadData();
    api.getGroupList({ pageNum: 1, pageSize: 99999, status: 0 }).then((res) => {
      this.groupOptions = res.data;
    });
    //工单状态
    this.getDicts("ledger_order_status").then((response) => {
      this.statusOptions = response.data;
    });
    //工单来源
    this.getDicts("ledger_demand_source").then((response) => {
      this.originOptions = response.data;
    });
    this.getDicts("support_dept").then((response) => {
      this.supportDeptOptions = response.data;
    });
    // 获取风险相关字典数据
    this.getDicts("risk_type").then((response) => {
      this.riskTypeOptions = response.data;
    });
    this.getDicts("risk_level").then((response) => {
      this.riskLevelOptions = response.data;
    });
    this.getDicts("risk_status").then((response) => {
      this.riskStatusOptions = response.data;
    });
    this.getBdNames();
    api.getRoleList({ pageNum: 1, pageSize: 99999, status: 0 }).then((res) => {
      this.roleOptions = res.data;
    });
    this.getTreeselect();
    this.getListUser();
    // this.getBusinessOptions("");
    queryDeptOrderTree({}).then((res) => {
      this.orderTypeOptions = res.data?.map((x) => {
        return { ...x, disabled: true };
      });
    });
    queryTreeList({}).then((res) => {
      this.businessTypeOptions = res.data;
    });
    // Promise.all([]).then(() => {
    //   setTimeout(() => {
    //     this.$nextTick(() => {

    //     });
    //   }, 500);
    // });
  },
  methods: {
    getBdNames() {
      api.getBdNames({}).then((res) => {
        this.bdNameOptions = res.data?.map((x) => {
          return { value: x, label: x };
        });
      });
    },
    handleBatchEnd() {
      if (this.selectedData.length == 0) {
        this.$message.warning("请至少勾选一条数据");
        return;
      }
      this.operationType = "batchEnd";
      this.getListUser();
      this.$refs.crud.switchModalView(true, "batchEnd", {
        ...initParams(this.modalConfig.formConfig),
        orderNoList: this.selectedData?.map((x) => x.orderNo),
        count: this.selectedData.length,
      });
    },
    handleBatchSelect(arr) {
      console.log(arr, "已选择");
      this.selectedData = arr;
    },
    async handleCustom() {
      this.storeData = this.$refs.crud.getVxeTableRef()?.getCustomStoreData();
      const method = this.isCustomColumn
        ? "cancelCustomColumn"
        : "saveCustomColumn";
      this.btnLoading = true;
      const res = await api[method]({
        menuFlag: "ledgerList",
        preferenceJson: JSON.stringify(this.storeData),
      }).catch(() => {
        this.btnLoading = false;
      });
      this.btnLoading = false;
      const text = this.isCustomColumn ? "偏好设置已取消" : "偏好设置已开启";
      if (res.code === "10000") {
        this.isCustomColumn = !this.isCustomColumn;
        this.$message.success(text);
        this.restorePromise = null;
      }
    },
    async initFilterPreference() {
      try {
        const res = await api.queryCustomColumn({
          menuFlag: "ledgerListFilter", // 使用不同的menuFlag区分筛选条件偏好设置
        });
        if (res.code === "10000") {
          this.isFilterCustom = res.data.preferenceFlag === "Y";
          if (this.isFilterCustom && res.data.preferenceJson) {
            this.selectedFilterFields = JSON.parse(res.data.preferenceJson);
          }
        }
      } catch (error) {
        console.error("Failed to load filter preferences:", error);
      }
    },
    async handleFilterCustom() {
      const method = this.isFilterCustom
        ? "cancelCustomColumn"
        : "saveCustomColumn";
      this.filterBtnLoading = true;
      const res = await api[method]({
        menuFlag: "ledgerListFilter", // 使用不同的menuFlag区分筛选条件偏好设置
        preferenceJson: JSON.stringify(this.selectedFilterFields),
      }).catch(() => {
        this.filterBtnLoading = false;
      });
      this.filterBtnLoading = false;
      const text = this.isFilterCustom
        ? "筛选条件偏好设置已取消"
        : "筛选条件偏好设置已开启";
      if (res.code === "10000") {
        this.isFilterCustom = !this.isFilterCustom;
        this.$message.success(text);
        this.filterRestorePromise = null;
      }
    },
    async handleFilterFieldsChange(fields) {
      this.selectedFilterFields = fields;
      if (this.isFilterCustom) {
        const res = await api.saveCustomColumn({
          preferenceJson: JSON.stringify(fields),
          menuFlag: "ledgerListFilter",
        });
        if (res.code === "10000") {
          this.$message.success("保存成功");
        }
      }
    },
    handleCommand(command, row) {
      this.btnArr?.find((x) => x.typeName == command)?.event(row);
    },
    listAllUser() {
      listAllUser({ status: "0" }).then((res) => {
        this.userOptions = res.data.map((x) => {
          return {
            ...x,
            value: x.userId,
            label: x.nickName + "-" + x.userName,
          };
        });
      });
    },
    handleSearch(list) {
      this.params.obscureParamsList = list;
      this.loadData();
    },
    handleSearchDrawer() {
      this.$refs.formSearchDrawer.open(this.params);
    },
    //加单
    handleAddOrder(row) {
      this.$refs.addOrderDrawer.open(row);
    },
    getUrgencyOptions(params) {
      api
        .getUrgencyDegree({
          pageSize: 99999,
          pageNum: 1,
          status: 0,
          ...params,
        })
        .then((res) => {
          this.urgencyDegreeOptions = res.data;
        });
    },
    handleQuery() {
      this.tablePage.currentPage = 1;
      this.loadData();
    },
    // 处理树形结构
    treeChange(arr) {
      return arr.map((item) => {
        if (item.children && item.children.length > 0) {
          this.treeChange(item.children);
        } else {
          delete item.children;
        }
        return item;
      });
    },
    /** 查询部门下拉树结构 */
    getTreeselect() {
      listDept({}).then((response) => {
        this.deptOptions = this.treeChange(response.data);
      });
    },
    //后台返回的数据如果和VueTreeselect要求的数据结构不同，需要进行转换
    normalizer(node) {
      //去掉children=[]的children属性
      if (node.childrenList && !node.childrenList.length) {
        delete node.childrenList;
      }
      return {
        id: node.deptId,
        label: node.deptName,
        children: node.children,
      };
    },
    // 节点单击事件
    handleNodeClick(data) {
      this.$refs.crud.setFormFields({ handleUser: undefined });
      this.getListUser({ orgNo: data.deptId });
    },
    beforeClearAll() {
      this.$refs.crud.setFormFields({
        handleUser: undefined,
        deptId: undefined,
      });
      this.getListUser();
    },
    //获取用户列表
    async getListUser(param) {
      let params = { ...param, pageNum: 1, pageSize: 9999 };
      const res = await listAllUser(params);
      if (res.code != 10000) return;
      this.userOption = res.data?.map((x) => {
        return {
          ...x,
          value: x.userId,
          label: x.userName + "-" + x.nickName + "-" + (x.phonenumber || ""),
        };
      });
    },
    //获取业务类型
    // getBusinessOptions(val) {
    //   api.queryBusinessByDept({ supportDept: val }).then((res) => {
    //     this.businessTypeOptions = res.data;
    //   });
    // },
    handleDeptChange(val) {
      this.params.orderTypeArr = undefined;
      this.params.businessType = undefined;
      this.params.urgencyLevel = undefined;
      // this.getBusinessOptions(val);
      // this.urgencyDegreeOptions = [];
      // this.orderTypeOptions = [];
      // if (val) {
      //   queryOrderTreeList({ supportDepts: val }).then((res) => {
      //     this.orderTypeOptions = res.data;
      //   });
      // } else {
      //   this.orderTypeOptions = [];
      // }
    },
    //批量导入
    handleBatchTypes() {
      this.$refs.batchUploadTypes.open();
    },
    cleanTree(arr) {
      return arr.map((item) => {
        // 复制当前对象
        const newItem = { ...item };

        // 处理子节点
        if (newItem.children) {
          // 递归处理子节点
          newItem.children = this.cleanTree(newItem.children);

          // 如果当前层级为 3 且子节点为空，删除 children 属性
          if (newItem.children.length === 0) {
            delete newItem.children;
          }
        }

        return newItem;
      });
    },
    getCityRegionData() {
      queryCityTree({}).then((res) => {
        // this.regionData = this.cleanTree(res.data);
        this.regionData = res.data;
      });
    },

    JumpToHandle(row) {
      this.$router.push({
        path: "/ledger/ledgerManage/handle",
        query: {
          orderId: row.orderId,
          orderNo: row.orderNo,
          flowKey: row.flowKey,
        },
      });
    },
    jumpToRecord(row) {
      // 目标时间 2025年1月23日 23:59:59
      const targetTime = moment("2025-01-23 23:59:59", "YYYY-MM-DD HH:mm:ss");
      // 需要判断的时间
      const checkTime = moment(row.createTime, "YYYY-MM-DD HH:mm:ss");
      const isOld = checkTime.isBefore(targetTime);

      this.$router.push({
        name: isOld ? "ledgerAddRecord" : "ledgerNewAddRecord",
        params: { [isOld ? "mainOrderNo" : "orderNo"]: row.orderNo },
      });
    },
    //查看流程节点超时情况
    handleDrawer(row) {
      this.$refs.processExceedDrawer.open(row);
    },
    //查看流程详情
    handleDetailDrawer(row) {
      this.$refs.processDetailDrawer.open(row);
    },
    //复制
    handleCopy(row) {
      this.$confirm("确定复制该工单吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then((res) => {
        let params = {
          orderId: row.orderId,
        };
        api.copy(params).then((res) => {
          this.$message.success("复制成功");
          this.loadData();
        });
      });
    },
    //跳转到新增/编辑/加单
    JumpToEdit(row, type = "add") {
      this.$router.push({
        path: `/ledger/ledgerManage/${type}`,
        query: {
          type: type,
          orderId: row.orderId,
          orderNo: row.orderNo,
        },
      });
    },
    jumpToDetail(row) {
      this.$router.push({
        path: "/ledger/ledgerManage/detail",
        query: {
          orderId: row.orderId,
          orderNo: row.orderNo,
          flowKey: row.flowKey,
          orderType: 1,
        },
      });
    },
    //删除
    deleteRowHandler(row) {
      this.$confirm("确定删除该工单吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then((res) => {
        let params = {
          orderNo: row.orderNo,
        };
        api.delete(params).then((res) => {
          this.$message.success("删除成功");
          this.loadData();
        });
      });
    },
    //转派
    handleTransfer(row) {
      this.operationType = "transfer";
      this.getListUser();
      this.$refs.crud.switchModalView(true, "transfer", {
        ...initParams(this.modalConfig.formConfig),
        orderNo: row.orderNo,
      });
    },
    //撤回
    handleWithdraw(row) {
      this.$refs.crud.switchModalView(true, "withdraw", {
        reCallRemark: "",
        orderNo: row.orderNo,
      });
    },
    //备注
    handleRemark(row) {
      this.operationType = "remark";
      this.$refs.crud.switchModalView(true, "remark", {
        ...initParams(this.modalConfig.formConfig),
        orderTypeEnum: row.mainOrderNo ? "2" : "1",
        orderNo: row.orderNo,
      });
    },
    //关联申请单号
    handleAssociate(row) {
      this.operationType = "associate";
      this.$refs.crud.switchModalView(true, "associate", {
        ...initParams(this.modalConfig.formConfig),
        orderId: row.orderId,
        orderNo: row.orderNo,
        applyNo: row.applyNo,
      });
    },
    //查看审批进度
    handleProcess(row) {
      this.$refs.checkProcessDrawer.open(row);
    },
    tabRadioChange(val) {
      this.activeTab = val;

      this.handleQuery();
    },

    handleCancelCustom() {
      this.$refs.crud.switchModalView(false);
    },

    checkPermission,

    handleExport() {
      const { businessType, orderTypeArr, supportDept, ...rest } = this.params;
      //数据是双向绑定的，所以直接获取data中定义的params即可获取到所有筛选项的值

      // 处理业务类型多选数据
      const businessTypeIds = this.processMultiCascaderData(businessType);

      // 处理工单类型多选数据
      const orderTypeIds = this.processMultiCascaderData(orderTypeArr);

      let params = {
        ...rest,
        oneBusinessTypeIds: businessTypeIds.level1 || [],
        twoBusinessTypeIds: businessTypeIds.level2 || [],
        threeBusinessTypeIds: businessTypeIds.level3 || [],
        oneOrderTypeIds: orderTypeIds.level2 || [],
        twoOrderTypeIds: orderTypeIds.level3 || [],
        threeOrderTypeIds: orderTypeIds.level4 || [],
        supportDepts: Array.isArray(supportDept)
          ? supportDept
          : supportDept
          ? [supportDept]
          : [],
        // pageNum: this.tablePage.currentPage,
        // pageSize: this.tablePage.pageSize,
      };
      params[this.tabActiveTab] = true;
      if (Array.isArray(params.region)) {
        params["provinceList"] = [];
        params["cityList"] = [];
        params["countyList"] = [];
        params.region.forEach((item) => {
          if (item[0]) {
            params["provinceList"].push(item[0]);
          }
          if (item[1]) {
            params["cityList"].push(item[1]);
          }
          if (item[2]) {
            params["countyList"].push(item[2]);
          }
        });
        delete params.region;
      }
      this.handleTimeRange(params);
      this.handleCommonExport(api.export, params);
    },
    //处理时间范围参数
    handleTimeRange(params) {
      const arr = [
        {
          field: "submitTime",
          title: "提交时间",
          startFieldName: "createStartTime",
          endFieldName: "createEndTime",
        },
        {
          field: "completeTime",
          title: "完成时间",
          startFieldName: "finishStartTime",
          endFieldName: "finishEndTime",
        },
        {
          field: "activityTime",
          title: "活动时间",
          startFieldName: "dingStartTime",
          endFieldName: "dingEndTime",
        },
        // {
        //   field: "problemHappenTime",
        //   title: "问题产生时间",
        //   startFieldName: "problemHappenStartTime",
        //   endFieldName: "problemHappenEndTime",
        // },
      ];
      arr.map((x) => {
        if (Array.isArray(params[x.field])) {
          params[x.startFieldName] = params[x.field][0] + " 00:00:00";
          params[x.endFieldName] = params[x.field][1] + " 23:59:59";
          delete params[x.field];
        }
      });
      if (Array.isArray(params.problemHappenTime)) {
        params.problemHappenStartTime = params.problemHappenTime[0];
        params.problemHappenEndTime = params.problemHappenTime[1];
        delete params.problemHappenTime;
      }
    },
    async loadData() {
      const {
        businessType,
        orderTypeArr,
        supportDept,
        obscureParamsList,
        ...rest
      } = this.params;
      //数据是双向绑定的，所以直接获取data中定义的params即可获取到所有筛选项的值
      const obscureParams = [];
      obscureParamsList?.forEach((x) => {
        if (x.name) {
          obscureParams.push(x.name);
        }
      });

      // 处理业务类型多选数据
      const businessTypeIds = this.processMultiCascaderData(businessType);

      // 处理工单类型多选数据
      const orderTypeIds = this.processMultiCascaderData(orderTypeArr);

      let params = {
        ...rest,
        oneBusinessTypeIds: businessTypeIds.level1 || [],
        twoBusinessTypeIds: businessTypeIds.level2 || [],
        threeBusinessTypeIds: businessTypeIds.level3 || [],
        oneOrderTypeIds: orderTypeIds.level2 || [],
        twoOrderTypeIds: orderTypeIds.level3 || [],
        threeOrderTypeIds: orderTypeIds.level4 || [],
        supportDepts: Array.isArray(supportDept)
          ? supportDept
          : supportDept
          ? [supportDept]
          : [],
        obscureParams: obscureParams,
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
      };
      console.log(params, "params");
      params[this.tabActiveTab] = true;
      // 处理省市区数据
      if (Array.isArray(params.region)) {
        params["provinceList"] = [];
        params["cityList"] = [];
        params["countyList"] = [];
        params.region.forEach((item) => {
          if (item[0]) {
            params["provinceList"].push(item[0]);
          }
          if (item[1]) {
            params["cityList"].push(item[1]);
          }
          if (item[2]) {
            params["countyList"].push(item[2]);
          }
        });
        delete params.region;
      }

      this.handleTimeRange(params);
      this.loading = true;
      const res = await api.getTableData(params).finally(() => {
        this.loading = false;
      });
      this.loading = false;
      this.tableData = res.data;
      this.tablePage.total = res.total;
    },

    // 处理多选级联选择器数据
    processMultiCascaderData(cascaderData) {
      const result = {
        level1: [],
        level2: [],
        level3: [],
        level4: [],
      };

      // 如果数据为空，直接返回空结果
      if (!cascaderData) {
        return result;
      }

      // 处理旧数据格式（单选模式下可能是字符串或数字）
      if (!Array.isArray(cascaderData)) {
        // 如果是字符串或数字，将其视为第一级的选择
        result.level1.push(cascaderData);
        return result;
      }

      // 处理单个数组（非嵌套数组）的情况
      if (
        Array.isArray(cascaderData) &&
        cascaderData.length > 0 &&
        !Array.isArray(cascaderData[0])
      ) {
        // 如果是单个数组（如 [1, 2, 3]），将其视为一个完整的路径
        if (cascaderData.length >= 1) {
          result.level1.push(cascaderData[0]);
        }
        if (cascaderData.length >= 2) {
          result.level2.push(cascaderData[1]);
        }
        if (cascaderData.length >= 3) {
          result.level3.push(cascaderData[2]);
        }
        if (cascaderData.length >= 4) {
          result.level4.push(cascaderData[3]);
        }
        return result;
      }

      // 处理多选数据格式（嵌套数组）
      cascaderData.forEach((path) => {
        if (Array.isArray(path)) {
          // 根据路径长度确定层级
          if (path.length >= 1) {
            result.level1.push(path[0]);
          }
          if (path.length >= 2) {
            result.level2.push(path[1]);
          }
          if (path.length >= 3) {
            result.level3.push(path[2]);
          }
          if (path.length >= 4) {
            result.level4.push(path[3]);
          }
        }
      });

      return result;
    },
    handleReset() {
      this.tablePage = {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      };
      // this.orderTypeOptions = [];
      this.params = initParams(this.filterOptions.config);
      this.loadData();
    },

    //弹窗确认按钮事件
    async modalConfirmHandler({ crudOperationType, ...formParams }) {
      return new Promise(async (resolve) => {
        let params = { ...formParams };
        console.log(crudOperationType, formParams, "提交");
        // crudOperationType:transfer/withdraw/remark/end/associate/batchEnd
        const res = await api[crudOperationType](params).catch(() => {
          resolve(false);
        });
        if (res?.code === "10000") {
          this.$message.success("提交成功");
          this.loadData();
          if (crudOperationType == "batchEnd") {
            this.$refs.crud.tableDeselectHandler();
          }
          resolve(true);
        } else {
          resolve(false);
        }
      });
    },

    getTagType(projectType) {
      const arr = [
        { type: "success", status: "新建" },
        { type: "warning", status: "增桩" },
        { type: "danger", status: "减桩" },
      ];
      return arr.find((x) => x.status == projectType)?.type;
    },
    querySearch(queryString, cb, api) {
      comApi[api]({
        name: queryString,
        companyName: queryString,
      }).then((res) => {
        const result = res.data?.map((x) => {
          return { value: x.companyName || x };
        });
        cb(result);
      });
    },
    //完结
    handleEnd(row) {
      this.operationType = "end";
      this.$refs.crud.switchModalView(true, "end", {
        ...initParams(this.modalConfig.formConfig),
        orderNo: row.orderNo,
      });
    },
    //审核
    handleCheck(row) {
      this.$refs.checkOrderDrawer.open(row);
    },
    handleBusinessChange(val) {
      // this.params.orderTypeArr = undefined;
      // this.params.urgencyLevel = undefined;
      // 不再使用直接引用的方式控制下拉菜单
    },
    handleAcceptOrder(row) {
      this.$confirm("确定要接单吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then((res) => {
        let params = {
          orderNo: row.orderNo,
        };
        api.acceptOrder(params).then((res) => {
          this.$message.success("接单成功");
          this.loadData();
        });
      });
    },
    getActCityList() {
      api.getActCityList({}).then((res) => {
        this.actCityOptions = res.data;
      });
    },
  },
  computed: {
    ...mapGetters(["userId", "roles"]),
    showSelectNum() {
      return this.selectedData?.length > 0;
    },
    //系统管理员
    isAdminRole() {
      return this.roles.includes("admin");
    },
    tableProps() {
      const that = this;
      return {
        border: true,
        align: "center",
        resizable: true,
        showOverflow: "tooltip",
        toolbarConfig: {
          custom: true,
          slots: {
            buttons: "toolbar_buttons",
          },
        },
        rowConfig: {
          keyField: "orderId",
          isCurrent: true,
        },
        checkboxConfig: {
          // checkRowKeys: selectRowsId,
          reserve: true,
          // 添加checkMethod方法，用于根据条件禁用复选框
          checkMethod({ row }) {
            // 处理中&&加单审核状态不是待审核
            const condition1 =
              ["1"].includes(row.orderStatus) && row.auditResult != 0;
            // 系统管理员/当前节点的处理人 / 处理组/ 处理角色
            const condition2 =
              that.isAdminRole || row.operateUserIds?.includes(that.userId);

            // 如果满足条件，则允许选择，否则禁用复选框
            return condition1 && condition2;
          },
        },
        customConfig: {
          storage: true,
          async restoreStore({ id, type, storeData }) {
            console.log("restoreStore", id, type, storeData);
            // 如果未发起过请求，则创建 Promise
            if (!that.restorePromise) {
              that.restorePromise = (async () => {
                try {
                  const res = await api.queryCustomColumn({
                    menuFlag: "ledgerList",
                  });
                  that.isCustomColumn = res.data.preferenceFlag === "Y";
                  // 根据服务端数据或默认值返回
                  return that.isCustomColumn
                    ? JSON.parse(res.data.preferenceJson)
                    : that.storeData;
                } catch (e) {
                  return {}; // 失败返回空
                }
              })();
            }
            // 返回缓存的 Promise
            return that.restorePromise;
          },
          updateStore({ id, type, storeData }) {
            // 原有保存逻辑不变
            return new Promise(async (resolve) => {
              that.storeData = storeData;
              if (that.isCustomColumn) {
                const res = await api.saveCustomColumn({
                  preferenceJson: JSON.stringify(storeData),
                  menuFlag: "ledgerList",
                });
                if (res.code === "10000") {
                  that.$message.success("保存成功");
                }
              }
              resolve();
            });
          },
        },

        id: "ledgerListId",
      };
    },
    tableColumn() {
      return [
        {
          type: "checkbox",
          width: 60,
        },
        {
          field: "orderNo",
          title: "工单单号",
          width: 180,
          slots: {
            default: ({ row }) => {
              return (
                <el-link
                  on={{
                    click: () => {
                      return this.jumpToDetail(row);
                    },
                  }}
                >
                  {row.orderNo}
                </el-link>
              );
            },
          },
        },
        // {
        //   field: "mainOrderNo",
        //   title: "主工单单号",
        //   width: 180,
        //   slots: {
        //     default: ({ row }) => {
        //       return [
        //         <el-button
        //           type="text"
        //           size="large"
        //           on={{
        //             click: () =>
        //               this.jumpToDetail({
        //                 orderNo: row.mainOrderNo,
        //                 orderId: row.mainOrderId,
        //                 flowKey: row.mainOrderFlowKey,
        //               }),
        //           }}
        //         >
        //           {row.mainOrderNo}
        //         </el-button>,
        //       ];
        //     },
        //   },
        // },
        {
          field: "supportDeptName",
          title: "支持部门",
          width: 100,
        },
        {
          field: "businessTypeStr",
          title: "业务类型",
          width: 150,
        },
        {
          field: "orderTypeStr",
          title: "工单类型",
          width: 150,
        },
        {
          field: "demandSourceName",
          title: "工单来源",
          width: 100,
        },
        {
          field: "urgencyLevelName",
          title: "紧急程度",
          width: 100,
        },
        {
          field: "orderDesc",
          title: "问题描述",
          width: 100,
        },
        {
          field: "stationName",
          title: "站点名称",
          width: 100,
        },
        // {
        //   field: "regionStr",
        //   title: "省市区",
        //   width: 150,
        // },
        {
          field: "companyName",
          title: "公司名称",
          width: 100,
        },
        {
          field: "companyCategory",
          title: "公司类别",
          width: 100,
        },
        {
          field: "companyAttribute",
          title: "公司属性",
          width: 100,
        },
        {
          field: "createUserName",
          title: "提交人",
          width: 100,
        },
        {
          field: "createTime",
          title: "提交时间",
          width: 150,
        },
        {
          field: "orderStatusName",
          title: "工单状态",
          width: 100,
        },
        {
          field: "handleUserName",
          title: "当前处理人",
          width: 120,
        },
        {
          field: "takeOrderUserName",
          title: "接单人",
          width: 120,
        },
        {
          field: "handleGroupName",
          title: "处理组",
          width: 120,
        },
        {
          field: "handleRoleName",
          title: "处理角色",
          width: 120,
        },
        {
          field: "finishTime",
          title: "完成时间",
          width: 150,
        },
        {
          field: "finishWay",
          title: "完成方式",
          width: 150,
          formatter: ({ cellValue }) => {
            if (cellValue == 1) {
              return "正常结束";
            } else if (cellValue == 4) {
              return "作废";
            } else if (cellValue == 5) {
              return "手动完结";
            }
          },
        },
        {
          field: "progress",
          title: "工单进度",
          slots: { default: "orderProcess" },
          width: 150,
        },
        {
          field: "totalNode",
          title: "流程总节点",
          width: 100,
        },
        {
          field: "baseInfo",
          title: "流程处理详情",
          width: 100,
          slots: {
            default: ({ row }) => {
              return [
                <el-button
                  type="text"
                  size="large"
                  on={{
                    click: () => this.handleDetailDrawer(row),
                  }}
                >
                  {row.currentNodeName}
                </el-button>,
              ];
            },
          },
        },
        {
          field: "nodeIsTimeout",
          title: "流程节点是否超时",
          width: 100,
          slots: {
            default: ({ row }) => {
              return [
                <el-button
                  style={{
                    color:
                      row.nodeIsTimeout === "Y"
                        ? "red"
                        : row.nodeIsTimeout === "N"
                        ? "green"
                        : "",
                  }}
                  type="text"
                  size="large"
                  on={{
                    click: () => this.handleDrawer(row),
                  }}
                >
                  {row.nodeIsTimeout === "Y"
                    ? "是"
                    : row.nodeIsTimeout === "N"
                    ? "否"
                    : row.nodeIsTimeout}
                </el-button>,
              ];
            },
          },
        },
        {
          field: "duration",
          title: "工单耗时",
          width: 150,
        },
        {
          field: "orderIsTimeout",
          title: "工单是否超时",
          width: 100,
          slots: {
            default: ({ row }) => {
              return [
                <div
                  style={{
                    color:
                      row.orderIsTimeout === "Y"
                        ? "red"
                        : row.orderIsTimeout === "N"
                        ? "green"
                        : "",
                  }}
                >
                  {row.orderIsTimeout === "Y"
                    ? "是"
                    : row.orderIsTimeout === "N"
                    ? "否"
                    : ""}
                </div>,
              ];
            },
          },
        },
        {
          field: "timeOutDuration",
          title: "超时时长",
          width: 150,
        },
        {
          field: "totalOrderTime",
          title: "工单总工时（h）",
          width: 150,
        },
        {
          field: "totalAddTime",
          title: "加单总工时（h）",
          width: 150,
        },
        {
          field: "addOrderCount",
          title: "加单次数",
          width: 100,
          slots: {
            default: ({ row }) => {
              return [
                <el-button
                  type="text"
                  size="large"
                  on={{
                    click: () => this.jumpToRecord(row),
                  }}
                >
                  {row.addOrderCount || 0}
                </el-button>,
              ];
            },
          },
        },
        {
          field: "auditResult",
          title: "加单审核状态",
          width: 150,
          formatter: ({ cellValue }) => {
            if (cellValue == 0) {
              return "待审核";
            } else if (cellValue == 1) {
              return "审核通过";
            } else if (cellValue == 2) {
              return "审核不通过";
            }
          },
        },
        {
          field: "remark",
          title: "备注",
          width: 150,
        },
        {
          field: "orderAttributes",
          title: "工单属性",
          width: 150,
          formatter: ({ cellValue }) => {
            if (cellValue == 1) {
              return "标准需求";
            } else if (cellValue == 2) {
              return "非标准需求";
            }
          },
        },
        {
          field: "riskType",
          title: "风险类型",
          width: 120,
        },
        {
          field: "riskLevel",
          title: "风险等级",
          width: 120,
        },
        {
          field: "riskStatus",
          title: "风险状态",
          width: 120,
        },
        // {
        //   field: "solution",
        //   title: "解决方案",
        //   width: 150,
        // },
        {
          field: "followUp",
          title: "后续跟进",
          width: 150,
        },
        {
          field: "bdName",
          title: "BD责任人",
          width: 120,
        },
        {
          field: "problemHappenTime",
          title: "问题产生时间",
          width: 150,
        },
        {
          field: "handleResult",
          title: "处理结果",
          width: 150,
        },
        {
          field: "dingBizNo",
          title: "钉钉工单审批编号",
          width: 150,
        },
        {
          field: "dingApplicant",
          title: "钉钉工单发起申请人",
          width: 150,
        },
        {
          field: "activeTime",
          title: "活动时间",
          width: 150,
        },
        {
          field: "dingCityName",
          title: "活动城市",
          width: 150,
        },
        {
          field: "finishRemark",
          title: "工单完成方式说明",
          width: 150,
        },
      ];
    },
    filterOptions() {
      let config = [
        // 默认显示的8个条件放在前面
        {
          field: "orderStatus",
          title: "工单状态",
          element: "el-select",
          props: {
            options: this.statusOptions,
            optionLabel: "dictLabel", //自定义选项名
            optionValue: "dictValue", //自定义选项值
            filterable: true,
          },
        },
        {
          field: "orderNo",
          element: "el-input",
          title: "工单单号",
        },
        {
          field: "businessType",
          title: "业务类型",
          element: "el-cascader",
          ref: "businessCascader",
          props: {
            popperClass: "location",
            collapseTags: true,
            props: {
              expandTrigger: "hover",
              checkStrictly: true,
              multiple: true,
              value: "id",
              label: "typeName",
              children: "childrenList",
            },
            filterable: true,
            options: this.businessTypeOptions,
          },
          on: {
            change: this.handleBusinessChange,
          },
        },
        {
          field: "orderTypeArr",
          title: "工单类型",
          element: "el-cascader",
          ref: "orderCascader",
          props: {
            popperClass: "location",
            collapseTags: true,
            props: {
              expandTrigger: "hover",
              checkStrictly: true,
              multiple: true,
              value: "id",
              label: "typeName",
              children: "childrenList",
            },
            filterable: true,
            options: this.orderTypeOptions,
          },
          on: {
            change: () => {
              // 不再使用直接引用的方式控制下拉菜单
            },
          },
        },
        {
          field: "supportDept",
          title: "支持部门",
          element: "el-select",
          props: {
            options: this.supportDeptOptions,
            optionLabel: "dictLabel", //自定义选项名
            optionValue: "dictValue", //自定义选项值
            filterable: true,
            multiple: true,
            collapseTags: true,
          },
          on: {
            // change: this.handleDeptChange,
          },
        },
        {
          field: "handleUserName",
          element: "el-input",
          title: "处理人",
        },
        {
          field: "submitTime",
          title: "提交时间",
          element: "el-date-picker",
          props: {
            type: "daterange",
            valueFormat: "yyyy-MM-dd",
          },
        },
        {
          field: "completeTime",
          title: "完成时间",
          element: "el-date-picker",
          props: {
            type: "daterange",
            valueFormat: "yyyy-MM-dd",
          },
        },
        // 其他条件放在后面，默认收起
        {
          field: "orderIsTimeout",
          title: "工单是否超时",
          element: "el-select",
          props: {
            options: [
              { value: "Y", label: "是" },
              { value: "N", label: "否" },
            ],
          },
        },
        {
          field: "createUserName",
          element: "el-input",
          title: "提交人",
        },
        {
          field: "nodeIsTimeout",
          title: "流程节点是否超时",
          element: "el-select",
          props: {
            options: [
              { value: "Y", label: "是" },
              { value: "N", label: "否" },
            ],
          },
        },
        {
          field: "orderDesc",
          element: "el-input",
          title: "问题描述",
        },
        // {
        //   field: "handleGroup",
        //   element: "el-select",
        //   title: "处理组",
        //   props: {
        //     filterable: true,
        //     options: this.groupOptions,
        //     optionValue: "groupId",
        //     optionLabel: "groupName",
        //   },
        // },
        // {
        //   field: "handleRole",
        //   element: "el-select",
        //   title: "处理角色",
        //   props: {
        //     filterable: true,
        //     options: this.roleOptions,
        //     optionValue: "roleId",
        //     optionLabel: "roleName",
        //   },
        // },
        {
          field: "demandSource",
          title: "工单来源",
          element: "el-select",
          props: {
            options: this.originOptions,
            optionLabel: "dictLabel", //自定义选项名
            optionValue: "dictValue", //自定义选项值
            filterable: true,
          },
        },
        {
          field: "companyName",
          title: "公司名称",
          element: "el-autocomplete",
          props: {
            fetchSuggestions: (queryString, cb) => {
              return this.querySearch(queryString, cb, "queryList");
            },
          },
        },
        {
          field: "urgencyLevel",
          title: "紧急程度",
          element: "el-select",
          props: {
            options: this.urgencyDegreeOptions,
            optionLabel: "urgencyName", //自定义选项名
            optionValue: "urgencyId", //自定义选项值
            filterable: true,
          },
        },
        {
          field: "region",
          title: "省市区",
          element: "custom-cascader",
          attrs: {
            collapseTags: true,
            clearable: true,
            props: {
              checkStrictly: true,
              multiple: true,
              // value: "areaCode",
              // label: "areaName",
            },
            options: regionData,
          },
        },
        {
          field: "companyCategory",
          title: "公司类别",
          element: "el-autocomplete",
          props: {
            fetchSuggestions: (queryString, cb) => {
              return this.querySearch(queryString, cb, "queryCompanyType");
            },
          },
        },
        {
          field: "companyAttribute",
          title: "公司属性",
          element: "el-autocomplete",
          props: {
            fetchSuggestions: (queryString, cb) => {
              return this.querySearch(queryString, cb, "queryCompanyAttrs");
            },
          },
        },
        {
          field: "orderAttributes",
          title: "工单属性",
          element: "el-select",
          props: {
            options: [
              { value: "1", label: "标准需求" },
              { value: "2", label: "非标准需求" },
            ],
          },
        },
        {
          field: "allHandleUsers",
          title: "节点处理人包含",
          element: "el-select",
          props: {
            options: this.userOptions,
            filterable: true,
            multiple: true,
          },
          defaultValue: [],
        },
        {
          field: "auditResult",
          title: "加单审核状态",
          element: "el-select",
          props: {
            options: [
              { value: "0", label: "待审核" },
              { value: "1", label: "审核通过" },
              { value: "2", label: "审核不通过" },
            ],
          },
        },
        {
          field: "finishWay",
          title: "工单完成方式",
          element: "el-select",
          props: {
            options: [
              { value: "1", label: "正常结束" },
              { value: "4", label: "作废" },
              { value: "5", label: "手动完结" },
            ],
          },
        },
        {
          field: "stationName",
          element: "el-input",
          title: "站点名称",
        },
        {
          field: "dingActCityIdList",
          title: "活动城市",
          element: "el-select",
          props: {
            filterable: true,
            clearable: true,
            multiple: true,
            optionLabel: "cityName",
            optionValue: "id",
            options: this.actCityOptions,
          },
        },
        {
          field: "activityTime",
          title: "活动时间",
          element: "el-date-picker",
          props: {
            type: "daterange",
            valueFormat: "yyyy-MM-dd",
          },
        },
        {
          field: "dingBizNo",
          element: "el-input",
          title: "钉钉审批号",
          attrs: {
            placeholder: "请输入钉钉审批号",
            maxlength: 50,
          },
        },
        {
          field: "dingApplicant",
          element: "el-input",
          title: "钉钉发起人",
          attrs: {
            placeholder: "请输入钉钉发起人",
            maxlength: 50,
          },
        },
        {
          field: "riskTypes",
          title: "风险类型",
          element: "el-select",
          props: {
            options: this.riskTypeOptions,
            optionLabel: "dictLabel",
            optionValue: "dictLabel",
            filterable: true,
            multiple: true,
            collapseTags: true,
          },
        },
        {
          field: "riskLevels",
          title: "风险等级",
          element: "el-select",
          props: {
            options: this.riskLevelOptions,
            optionLabel: "dictLabel",
            optionValue: "dictLabel",
            filterable: true,
            multiple: true,
            collapseTags: true,
          },
        },
        {
          field: "riskStatuses",
          title: "风险状态",
          element: "el-select",
          props: {
            options: this.riskStatusOptions,
            optionLabel: "dictLabel",
            optionValue: "dictLabel",
            filterable: true,
            multiple: true,
            collapseTags: true,
          },
        },
        {
          field: "bdNames",
          title: "BD责任人",
          element: "el-select",
          props: {
            options: this.bdNameOptions,
            filterable: true,
            multiple: true,
            collapseTags: true,
          },
        },
        {
          field: "problemHappenTime",
          title: "问题产生时间",
          element: "el-date-picker",
          props: {
            type: "daterange",
            valueFormat: "yyyy-MM-dd",
            startPlaceholder: "开始日期",
            endPlaceholder: "结束日期",
          },
        },
      ];
      config.forEach((item) => {
        item.show = this.selectedFilterFields.includes(item.field);
      });
      return {
        showCount: 8, //默认显示筛选项的个数 多余的展开展示
        layout: "right",
        inline: true,
        labelWidth: "140px",
        //筛选控件配置
        config: config,
        params: this.params,
      };
    },
    outBtnArr() {
      return [
        {
          title: "处理",
          typeName: "handle",
          slotName: "handle",
          showForm: false,
          event: (row) => {
            return this.JumpToHandle(row);
          },
          condition: (row) => {
            //处理中
            const condition1 = row.orderStatus == "1";
            //系统管理员/当前节点的处理人 / 处理组/ 处理角色
            const condition2 =
              this.isAdminRole || row.operateUserIds?.includes(this.userId);
            return (
              condition1 &&
              condition2 &&
              checkPermission(["ledger:list:handle"])
            );
          },
        },
        {
          title: "加单",
          typeName: "addOrder",
          event: (row) => {
            return this.handleAddOrder(row);
          },
          condition: (row) => {
            //处理中&&加单审核状态不是待审核
            const condition1 =
              ["1"].includes(row.orderStatus) && row.auditResult != 0;
            //已完成/已完结&&加单审核状态不是待审核/审核不通过
            const condition3 =
              ["2", "4"].includes(row.orderStatus) &&
              !["0", "2"].includes(row.auditResult);
            //系统管理员/当前节点的处理人 / 处理组/ 处理角色
            const condition2 =
              this.isAdminRole ||
              row.operateUserIds?.includes(this.userId) ||
              row.createUser === this.userId;
            return (
              (condition1 || condition3) &&
              condition2 &&
              checkPermission(["ledger:list:addOrder"])
            );
          },
        },
        {
          title: "接单",
          typeName: "acceptOrder",
          event: (row) => {
            return this.handleAcceptOrder(row);
          },
          condition: (row) => {
            //处理中
            const condition1 = ["1"].includes(row.orderStatus);
            //接单人为空
            const condition2 = !row.takeOrderUser;
            return (
              condition1 &&
              condition2 &&
              checkPermission(["ledger:list:acceptOrder"])
            );
          },
        },
      ];
    },
    btnArr() {
      return [
        {
          title: "编辑",
          typeName: "edit",
          event: (row) => {
            return this.JumpToEdit(row, "edit");
          },
          condition: (row) => {
            //草稿
            const condition1 = row.orderStatus == "0";
            //系统管理员/工单创建人
            const condition2 =
              this.isAdminRole || this.userId === row.createUser;
            return (
              condition1 && condition2 && checkPermission(["ledger:list:edit"])
            );
          },
        },
        {
          title: "审核",
          typeName: "check",
          slotName: "check",
          showForm: false,
          event: (row) => {
            return this.handleCheck(row);
          },
          condition: (row) => {
            //已完成/已完结&&加单审核状态为待审核/审核不通过
            const condition1 =
              ["2", "4"].includes(row.orderStatus) &&
              ["0", "2"].includes(row.auditResult);
            //处理中&&加单审核状态为待审核
            const condition2 = row.orderStatus == "1" && row.auditResult == "0";
            //系统管理员/加单审核人
            const condition3 =
              this.isAdminRole || row.auditUser === this.userId;
            return (
              (condition1 || condition2) &&
              condition3 &&
              checkPermission(["ledger:list:check"])
            );
          },
        },
        {
          title: "转派",
          typeName: "transfer",
          event: (row) => {
            return this.handleTransfer(row);
          },
          condition: (row) => {
            //处理中&&加单审核状态非待审核
            const condition1 = row.orderStatus == "1" && row.auditResult != "0";
            //系统管理员/当前节点的处理人 / 处理组/ 处理角色
            const condition2 =
              this.isAdminRole || row.operateUserIds?.includes(this.userId);
            return (
              condition1 &&
              condition2 &&
              checkPermission(["ledger:list:transfer"])
            );
          },
        },
        {
          title: "完结",
          typeName: "end",
          modalTitle: "手动完结工单",
          event: (row) => {
            return this.handleEnd(row);
          },
          condition: (row) => {
            //处理中&&加单审核状态不是待审核
            const condition1 =
              ["1"].includes(row.orderStatus) && row.auditResult != 0;
            //系统管理员/当前节点的处理人 / 处理组/ 处理角色
            const condition2 =
              this.isAdminRole || row.operateUserIds?.includes(this.userId);
            return (
              condition1 &&
              condition2 &&
              checkPermission(["ledger:list:endOrder"])
            );
          },
        },
        {
          title: "复制",
          typeName: "copy",
          // showForm: false,
          event: (row) => {
            return this.handleCopy(row);
          },
          condition: (row) => {
            //非草稿、已作废
            const condition = !["0", "3"].includes(row.orderStatus);
            return condition && checkPermission(["ledger:list:copy"]);
          },
        },
        {
          title: "删除",
          typeName: "delete",
          condition: (row) => {
            //草稿/已作废
            const condition1 = ["0", "3"].includes(row.orderStatus);
            //系统管理员/工单创建人
            const condition2 =
              this.isAdminRole || this.userId === row.createUser;
            return (
              condition1 &&
              condition2 &&
              checkPermission(["ledger:list:delete"])
            );
          },
          event: (row) => {
            return this.deleteRowHandler(row);
          },
        },
        {
          title: "备注",
          typeName: "remark",
          // showForm: false,
          event: (row) => {
            return this.handleRemark(row);
          },
          condition: (row) => {
            //处理中/已完成
            // const condition = ["1", "2"].includes(row.orderStatus);
            return checkPermission(["ledger:list:remark"]);
          },
        },
        {
          title: "详情",
          typeName: "detail",
          event: (row) => {
            return this.jumpToDetail(row);
          },
          condition: (row) => {
            return checkPermission(["ledger:list:detail"]);
          },
        },
        {
          title: "撤回草稿",
          typeName: "withdraw",
          slotName: "withdraw",
          showForm: false,
          event: (row) => {
            return this.handleWithdraw(row);
          },
          condition: (row) => {
            //处理中
            const condition1 = row.orderStatus == "1";
            //系统管理员/工单创建人
            const condition2 =
              this.isAdminRole || this.userId === row.createUser;
            return (
              condition1 &&
              condition2 &&
              checkPermission(["ledger:list:withdraw"])
            );
          },
        },
        // {
        //   title: "关联申请单号",
        //   modalTitle: "关联运管审批单号",
        //   typeName: "associate",
        //   event: (row) => {
        //     return this.handleAssociate(row);
        //   },
        //   condition: (row) => {
        //     return checkPermission(["ledger:list:associate"]);
        //   },
        // },
        {
          title: "查看审批进度",
          typeName: "process",
          event: (row) => {
            return this.handleProcess(row);
          },
          condition: (row) => {
            return checkPermission(["ledger:list:process"]);
          },
        },
      ];
    },
    modalConfig() {
      const form = {
        //转派
        transfer: [
          {
            field: "deptId",
            element: "slot",
            slotName: "deptTree",
            title: "选择部门",
          },
          {
            field: "targetUserId",
            element: "el-select",
            title: "选择人员",
            props: {
              options: this.userOption,
              filterable: true,
              placeholder: "请输入或选择人员",
            },
            rules: [{ required: true, message: "请选择人员" }],
          },
          {
            field: "transferRemark",
            element: "el-input",
            title: "转派原因",
            props: {
              type: "textarea",
            },
            attrs: {
              rows: 5,
              maxlength: 500,
              showWordLimit: true,
              placeholder: "请输入具体的原因描述，500个字符以内",
            },
          },
        ],
        //备注
        remark: [
          {
            field: "remark",
            element: "el-input",
            title: "备注信息",
            props: {
              type: "textarea",
            },
            attrs: {
              rows: 5,
              maxlength: 500,
              showWordLimit: true,
              placeholder: "请输入具体的描述，500个字符以内",
            },
            rules: [{ required: true, message: "备注不能为空！" }],
          },
          {
            field: "orderAttributes",
            element: "el-radio-group",
            title: "工单属性",
            props: {
              options: [
                { value: 1, label: "标准需求" },
                { value: 2, label: "非标需求" },
              ],
            },
            defaultValue: 1,
          },
        ],
        //完结
        end: [
          {
            field: "finishRemark",
            element: "el-input",
            title: "完结原因",
            props: {
              type: "textarea",
            },
            attrs: {
              rows: 5,
              maxlength: 500,
              showWordLimit: true,
              placeholder: "请输入具体的描述，500个字符以内",
            },
            rules: [{ required: true, message: "完结原因不能为空！" }],
          },
        ],
        //关联申请单号
        associate: [
          {
            field: "applyNo",
            element: "el-input",
            title: "申请单号",
            attrs: {
              maxlength: 20,
              placeholder: "请输入运管审批单号",
            },
            rules: [{ required: true, message: "请输入申请单号！" }],
          },
        ],
        //批量完结
        batchEnd: [
          {
            field: "count",
            title: "已选工单数",
            preview: {
              formatter: (item, params) => {
                return item.count + "个";
              },
            },
          },
          {
            field: "finishType",
            title: "数据处理方式",
            element: "el-radio-group",
            props: {
              options: [
                { value: 1, label: "将当前节点变成已处理" },
                { value: 2, label: "将所有节点变成已处理" },
              ],
            },
            defaultValue: 1,
            rules: [{ required: true, message: "请选择数据处理方式！" }],
          },
          {
            field: "finishRemark",
            element: "el-input",
            title: "完结原因",
            props: {
              type: "textarea",
            },
            attrs: {
              rows: 5,
              maxlength: 500,
              showWordLimit: true,
              placeholder: "请输入具体的描述，500个字符以内",
            },
            rules: [{ required: true, message: "完结原因不能为空！" }],
          },
        ],
      };
      return {
        // modalFullScreen: true,
        viewBtn: false,
        submitBtn: false,
        okText: "确认",
        cancelText: "取消",
        addBtn: false,
        editBtn: false,
        delBtn: false,
        menu: true,
        menuWidth: 200,
        menuFixed: "right",
        modalWidth: "50%",
        formConfig: form[this.operationType] || [],
        crudPermission: [
          {
            type: "delete",
            condition: (row) => {
              //草稿/已作废
              const condition1 = ["0", "3"].includes(row.orderStatus);
              //系统管理员/工单创建人
              const condition2 =
                this.isAdminRole || this.userId === row.createUser;
              return (
                condition1 &&
                condition2 &&
                checkPermission(["ledger:list:delete"])
              );
            },
          },
        ],
        customOperationTypes: [
          {
            title: "处理",
            typeName: "handle",
            slotName: "handle",
            showForm: false,
            event: (row) => {
              return this.JumpToHandle(row);
            },
            condition: (row) => {
              //处理中
              const condition1 = row.orderStatus == "1";
              //系统管理员/当前节点的处理人 / 处理组/ 处理角色
              const condition2 =
                this.isAdminRole || row.operateUserIds?.includes(this.userId);
              return (
                condition1 &&
                condition2 &&
                checkPermission(["ledger:list:handle"])
              );
            },
          },
          {
            title: "审核",
            typeName: "check",
            slotName: "check",
            showForm: false,
            event: (row) => {
              return this.handleCheck(row);
            },
            condition: (row) => {
              //已完成/已完结&&加单审核状态为待审核/审核不通过
              const condition1 =
                ["2", "4"].includes(row.orderStatus) &&
                ["0", "2"].includes(row.auditResult);
              //处理中&&加单审核状态为待审核
              const condition2 =
                row.orderStatus == "1" && row.auditResult == "0";
              //系统管理员/加单审核人
              const condition3 =
                this.isAdminRole || row.auditUser === this.userId;
              return (
                (condition1 || condition2) &&
                condition3 &&
                checkPermission(["ledger:list:check"])
              );
            },
          },
          {
            title: "转派",
            typeName: "transfer",
            event: (row) => {
              return this.handleTransfer(row);
            },
            condition: (row) => {
              //处理中&&加单审核状态非待审核
              const condition1 =
                row.orderStatus == "1" && row.auditResult != "0";
              //系统管理员/当前节点的处理人 / 处理组/ 处理角色
              const condition2 =
                this.isAdminRole || row.operateUserIds?.includes(this.userId);
              return (
                condition1 &&
                condition2 &&
                checkPermission(["ledger:list:transfer"])
              );
            },
          },
          {
            title: "编辑",
            typeName: "edit",
            event: (row) => {
              return this.JumpToEdit(row, "edit");
            },
            condition: (row) => {
              //草稿
              const condition1 = row.orderStatus == "0";
              //系统管理员/工单创建人
              const condition2 =
                this.isAdminRole || this.userId === row.createUser;
              return (
                condition1 &&
                condition2 &&
                checkPermission(["ledger:list:edit"])
              );
            },
          },
          {
            title: "加单",
            typeName: "addOrder",
            event: (row) => {
              return this.handleAddOrder(row);
            },
            condition: (row) => {
              //处理中&&加单审核状态不是待审核
              const condition1 =
                ["1"].includes(row.orderStatus) && row.auditResult != 0;
              //已完成/已完结&&加单审核状态不是待审核/审核不通过
              const condition3 =
                ["2", "4"].includes(row.orderStatus) &&
                !["0", "2"].includes(row.auditResult);
              //系统管理员/当前节点的处理人 / 处理组/ 处理角色
              const condition2 =
                this.isAdminRole || row.operateUserIds?.includes(this.userId);
              return (
                (condition1 || condition3) &&
                condition2 &&
                checkPermission(["ledger:list:addOrder"])
              );
            },
          },
          {
            title: "完结",
            typeName: "end",
            modalTitle: "手动完结工单",
            event: (row) => {
              return this.handleEnd(row);
            },
            condition: (row) => {
              //处理中&&加单审核状态不是待审核
              const condition1 =
                ["1"].includes(row.orderStatus) && row.auditResult != 0;
              //系统管理员/当前节点的处理人 / 处理组/ 处理角色
              const condition2 =
                this.isAdminRole || row.operateUserIds?.includes(this.userId);
              return (
                condition1 &&
                condition2 &&
                checkPermission(["ledger:list:endOrder"])
              );
            },
          },
          {
            title: "撤回草稿",
            typeName: "withdraw",
            slotName: "withdraw",
            showForm: false,
            event: (row) => {
              return this.handleWithdraw(row);
            },
            condition: (row) => {
              //处理中
              const condition1 = row.orderStatus == "1";
              //系统管理员/工单创建人
              const condition2 =
                this.isAdminRole || this.userId === row.createUser;
              return (
                condition1 &&
                condition2 &&
                checkPermission(["ledger:list:withdraw"])
              );
            },
          },
          {
            title: "复制",
            typeName: "copy",
            // showForm: false,
            event: (row) => {
              return this.handleCopy(row);
            },
            condition: (row) => {
              //非草稿、已作废
              const condition = !["0", "3"].includes(row.orderStatus);
              return condition && checkPermission(["ledger:list:copy"]);
            },
          },
          {
            title: "备注",
            typeName: "remark",
            // showForm: false,
            event: (row) => {
              return this.handleRemark(row);
            },
            condition: (row) => {
              //处理中/已完成
              // const condition = ["1", "2"].includes(row.orderStatus);
              return checkPermission(["ledger:list:remark"]);
            },
          },
          {
            title: "详情",
            typeName: "detail",
            event: (row) => {
              return this.jumpToDetail(row);
            },
            condition: (row) => {
              return checkPermission(["ledger:list:detail"]);
            },
          },
          // {
          //   title: "关联申请单号",
          //   modalTitle: "关联运管审批单号",
          //   typeName: "associate",
          //   event: (row) => {
          //     return this.handleAssociate(row);
          //   },
          //   condition: (row) => {
          //     return checkPermission(["ledger:list:associate"]);
          //   },
          // },
          {
            title: "查看审批进度",
            typeName: "process",
            event: (row) => {
              return this.handleProcess(row);
            },
            condition: (row) => {
              return checkPermission(["ledger:list:process"]);
            },
          },
          {
            title: "批量完结",
            typeName: "batchEnd",
            event: (row) => {},
            condition: (row) => {
              return false;
            },
          },
        ],
        formLayoutConfig: {
          defaultColSpan: 24,
          labelPosition: "right",
          labelWidth: "110px",
        },
      };
    },
  },
};
</script>

<style lang="less" scoped>
.line {
  border-bottom: 1px #e1e0e0 dashed;
}
.radio-tab /deep/ .el-radio-button--small .el-radio-button__inner {
  padding: 12px 36px;
}
.confirm-text {
  margin-bottom: 20px;
  text-align: center;
  font-size: 18px;
  font-weight: bold;
}
</style>
