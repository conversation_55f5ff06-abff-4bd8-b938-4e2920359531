<!-- 购电进度 -->
<template>
  <div>
    <BuseCrud
      ref="crud"
      :loading="loading"
      :filterOptions="filterOptions"
      :tablePage="tablePage"
      :pagerProps="pagerProps"
      :tableColumn="tableColumn"
      :tableData="tableData"
      :tableProps="tableProps"
      :modalConfig="modalConfig"
      @modalConfirm="modalConfirmHandler"
      @loadData="loadData"
      @rowDel="deleteRowHandler"
      @rowEdit="rowEdit"
      selectStyleType="infoTip"
      @handleBatchSelect="handleBatchSelect"
      :showSelectNum="showSelectNum"
    >
      <template slot="filterCustomBtn">
        <div class="btn-wrap">
          <el-button
            type="primary"
            icon="el-icon-download"
            @click.stop="handleExport"
            v-has-permi="['xdtCharge:purchaseProgress:export']"
            >导出
          </el-button>
          <el-button
            type="primary"
            icon="el-icon-search"
            @click.stop="handleQuery"
            >查询
          </el-button>
          <el-button icon="el-icon-refresh" @click.stop="handleReset"
            >重置
          </el-button>
        </div>
      </template>
      <template #toolbar_buttons>
        <el-select
          v-model="selectPage"
          size="mini"
          style="margin-right: 10px;width: 86px;"
        >
          <el-option label="当前页" value="1"></el-option>
          <el-option label="全部页" value="2"></el-option>
        </el-select>
        <el-button
          type="primary"
          @click="handleBatchImport"
          v-has-permi="['xdtCharge:purchaseProgress:import']"
          >导入</el-button
        >
        <el-button
          type="primary"
          @click="handleBatchAdd"
          v-has-permi="['xdtCharge:purchaseProgress:add']"
          >新增</el-button
        >
        <el-button
          type="primary"
          @click="handleBatchAdd"
          v-has-permi="['xdtCharge:purchaseProgress:batchAdd']"
          >批量新增</el-button
        >
        <el-button
          type="primary"
          @click="handleBatchOperation"
          v-has-permi="['xdtCharge:purchaseProgress:batchOperation']"
          >批量操作</el-button
        >
      </template>
      <template #modalFooter="{ row, crudOperationType }">
        <div v-if="crudOperationType === 'log'"></div>
      </template>
      <template slot="log" slot-scope="{ row, operationType }">
        <Timeline
          :list="recordList"
          operateTypeTitle="operatorTypeName"
          operatorNameTitle="operatorUserName"
          createTimeTitle="operatorTime"
          operateDetailTitle="remark"
        ></Timeline>
      </template>
    </BuseCrud>
    <BatchUpload
      @uploadSuccess="handleQuery"
      ref="batchUpload"
      title="批量导入购电进度"
      :uploadApi="uploadObj.api"
      :templateUrl="uploadObj.url"
      :extraData="uploadObj.extraData"
      :maxSize="0.01953"
      maxSizeText="20m"
    >
    </BatchUpload>
  </div>
</template>

<script>
import checkPermission from "@/utils/permission.js";
import api from "@/api/settlement/xdt/charge/purchaseProgress.js";
import { initParams } from "@/utils/buse.js";
import exportMixin from "@/mixin/export.js";
import { queryLog } from "@/api/common.js";
import Timeline from "@/components/Timeline/index.vue";

export default {
  name: "purchaseProgress",
  mixins: [exportMixin],
  components: {
    Timeline,
  },
  data() {
    return {
      selectPage: "1",
      workLoading: false,
      uploadObj: {
        api: "/st/newCharge/power/importExcel",
        url: "/charging-maintenance-ui/static/购电进度导入模板.xlsx",
        extraData: {},
      },
      tableTotal: 0,
      // 查询参数
      searchForm: {
        pageNum: 1,
        pageSize: 10,
      },
      visible: false,
      //buse参数-s
      tableProps: {
        border: true,
        align: "center",
        resizable: true,
        showOverflow: "tooltip",
        toolbarConfig: {
          custom: true,
          slots: {
            buttons: "toolbar_buttons",
          },
        },
        rowConfig: {
          keyField: "id",
          isCurrent: true,
        },
        checkboxConfig: {
          reserve: true,
        },
      },
      tableData: [],
      params: {},
      loading: false,
      pagerProps: {
        layouts: [
          "Total",
          "Sizes",
          "PrevPage",
          "JumpNumber",
          "NextPage",
          "FullJump",
        ],
        background: true,
        className: "pagination-container",
        pageSizes: [10, 20, 30, 50],
      },
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      },
      operationType: "accept",
      //buse参数-e

      // 下拉选项数据
      statusOptions: [],
      typeOptions: [],
    };
  },
  created() {
    this.params = {
      ...initParams(this.filterOptions.config),
    };
    // 获取下拉列表数据
    // this.loadDropListData();
  },
  methods: {
    checkPermission,
    handleBatchOperation() {
      if (!this.selectedData.length) {
        this.$message.warning("请至少勾选一条数据");
        return;
      }
      this.operationType = "batchOperation";
      this.$refs.crud.switchModalView(true, "batchOperation", {
        ...initParams(this.modalConfig.formConfig),
        idList: this.selectedData?.map((x) => x.id),
        //todo 选择全部页/当前页 新加一个字段
        selectPage: this.selectPage,
      });
    },
    handleBatchSelect(arr) {
      console.log(arr, "已选择");
      this.selectedData = arr;
    },
    handleBatchAdd() {
      this.$refs.crud.switchModalView(true, "add");
    },

    handleBatchImport() {
      // 导入功能实现
      console.log("导入功能");
    },

    // 加载下拉列表数据
    async loadDropListData() {
      try {
        const res = await api.getDropLists();
        if (res.success && res.data) {
          // 可以根据需要处理下拉列表数据
          console.log("下拉列表数据:", res.data);
        }
      } catch (error) {
        console.error("获取下拉列表数据失败:", error);
      }
    },
    handleLog(row) {
      queryLog({ businessId: row.id }).then((res) => {
        this.recordList = res.data;
      });
      this.$refs.crud.switchModalView(true, "log");
    },
    handleAdd() {
      this.isEdit = false;
      this.operationType = "update";
      this.$refs.crud.switchModalView(true, "ADD", {
        ...initParams(this.modalConfig.formConfig),
      });
    },
    rowEdit(row) {
      this.isEdit = true;
      this.operationType = "update";
      this.$refs.crud.switchModalView(true, "UPDATE", {
        ...initParams(this.modalConfig.formConfig),
        ...row,
      });
    },
    deleteRowHandler(row) {
      this.$confirm("是否确认删除？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then((res) => {
        let params = {
          id: row.id,
        };
        api.delete(params).then((res) => {
          this.$message.success("删除成功");
          this.loadData();
        });
      });
    },
    handleExport() {
      let params = {
        ...this.params,
      };
      this.handleTimeRange(params);
      this.handleCommonExport(api.export, params);
    },

    //处理时间范围参数
    handleTimeRange(params) {
      const arr = [
        {
          field: "billYearMonthRange",
          title: "账单年月",
          startFieldName: "billYearMonthStart",
          endFieldName: "billYearMonthEnd",
        },
      ];
      arr.map((x) => {
        if (Array.isArray(params[x.field])) {
          params[x.startFieldName] = params[x.field][0];
          params[x.endFieldName] = params[x.field][1];
          delete params[x.field];
        }
      });
    },

    async loadData() {
      let params = {
        ...this.params,
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
      };

      this.handleTimeRange(params);
      this.loading = true;
      const res = await api.list(params);
      this.loading = false;
      this.tableData = res.data;
      this.tablePage.total = res.total;
    },

    handleReset() {
      this.tablePage = {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      };
      this.params = initParams(this.filterOptions.config);
      this.loadData();
    },

    handleQuery() {
      this.tablePage.currentPage = 1;
      this.loadData();
    },

    //弹窗确认按钮事件
    async modalConfirmHandler({ crudOperationType, ...formParams }) {
      let params = { ...formParams };
      console.log(crudOperationType, formParams, "提交");

      try {
        if (crudOperationType === "add") {
          await api.add(params);
          this.$message.success("新增成功");
        } else if (crudOperationType === "edit") {
          await api.update(params);
          this.$message.success("更新成功");
        }
        this.loadData();
      } catch (error) {
        console.error("操作失败:", error);
        return false;
      }
    },
  },
  computed: {
    tableColumn() {
      return [
        {
          field: "billYearMonth",
          title: "账单年月",
          width: 120,
        },
        {
          field: "operator",
          title: "运营商",
          width: 150,
        },
        {
          field: "returnAmount",
          title: "回票金额（元）",
          width: 120,
        },
        {
          field: "reconciliationPerson",
          title: "对账负责人",
          width: 100,
        },
        {
          field: "billCheck",
          title: "账单核对",
          width: 100,
        },
        {
          field: "counterpartyConfirmation",
          title: "对方确认",
          width: 100,
        },
        {
          field: "counterpartySeal",
          title: "对方盖章",
          width: 100,
        },
        {
          field: "ourSeal",
          title: "我方盖章",
          width: 100,
        },
        {
          field: "returnComplete",
          title: "回票完成",
          width: 100,
        },
        {
          field: "remarks",
          title: "备注",
          width: 150,
        },
        {
          field: "mode",
          title: "模式",
          width: 100,
        },
        {
          field: "updateBy",
          title: "操作人",
          width: 100,
        },
        {
          field: "updateTime",
          title: "操作时间",
          width: 160,
        },
      ];
    },
    filterOptions() {
      return {
        showCount: 5, //默认显示筛选项的个数 多余的展开展示
        layout: "right",
        inline: true,
        labelWidth: "140px",
        //筛选控件配置
        config: [
          {
            field: "operator",
            element: "el-input",
            title: "运营商",
          },
          {
            field: "counterpartyConfirmation",
            title: "对方确认",
            element: "el-select",
            props: {
              options: [
                { label: "已确认", value: "1" },
                { label: "未确认", value: "0" },
              ],
              filterable: true,
            },
          },
          {
            field: "ourSeal",
            title: "我方盖章",
            element: "el-select",
            props: {
              options: [
                { label: "已盖章", value: "1" },
                { label: "未盖章", value: "0" },
              ],
              filterable: true,
            },
          },
          {
            field: "billYearMonthRange",
            title: "账单年月",
            element: "el-date-picker",
            props: {
              type: "monthrange",
              valueFormat: "yyyy-MM",
              placeholder: "请选择账单年月范围",
            },
          },
          {
            field: "billCheck",
            title: "账单核对",
            element: "el-select",
            props: {
              options: [
                { label: "已核对", value: "1" },
                { label: "未核对", value: "0" },
              ],
              filterable: true,
            },
          },
          {
            field: "returnComplete",
            title: "回票完成",
            element: "el-select",
            props: {
              options: [
                { label: "已完成", value: "1" },
                { label: "未完成", value: "0" },
              ],
              filterable: true,
            },
          },
        ],
        params: this.params,
      };
    },

    modalConfig() {
      return {
        viewBtn: false,
        submitBtn: false,
        okText: "确认",
        cancelText: "取消",
        addBtn: false,
        editBtn: checkPermission(["xdtCharge:purchaseProgress:edit"]),
        delBtn: checkPermission(["xdtCharge:purchaseProgress:delete"]),
        menu: true,
        menuWidth: 250,
        menuFixed: "right",
        modalWidth: "50%",
        formConfig: [
          {
            field: "billYearMonth",
            title: "账单年月",
            element: "el-date-picker",
            rules: [{ required: true, message: "请选择账单年月" }],
            props: {
              type: "month",
              valueFormat: "yyyy-MM",
              placeholder: "请选择账单年月",
            },
          },
          {
            field: "operator",
            title: "运营商",
            element: "el-input",
            rules: [{ required: true, message: "请输入运营商名称" }],
            props: {
              placeholder: "请输入运营商名称",
            },
          },
          {
            field: "returnAmount",
            title: "退回金额",
            element: "el-input-number",
            props: {
              precision: 2,
              min: 0,
              placeholder: "请输入退回金额",
            },
          },
          {
            field: "reconciliationPerson",
            title: "对账人",
            element: "el-input",
            props: {
              placeholder: "请输入对账人",
            },
          },
          {
            field: "billCheck",
            title: "账单核对",
            element: "el-select",
            props: {
              options: [
                { label: "已核对", value: "1" },
                { label: "未核对", value: "0" },
              ],
              optionLabel: "label",
              optionValue: "value",
              placeholder: "请选择账单核对状态",
            },
          },
          {
            field: "counterpartyConfirmation",
            title: "对方确认",
            element: "el-select",
            props: {
              options: [
                { label: "已确认", value: "1" },
                { label: "未确认", value: "0" },
              ],
              optionLabel: "label",
              optionValue: "value",
              placeholder: "请选择对方确认状态",
            },
          },
          {
            field: "counterpartySeal",
            title: "对方盖章",
            element: "el-select",
            props: {
              options: [
                { label: "已盖章", value: "1" },
                { label: "未盖章", value: "0" },
              ],
              optionLabel: "label",
              optionValue: "value",
              placeholder: "请选择对方盖章状态",
            },
          },
          {
            field: "ourSeal",
            title: "我方盖章",
            element: "el-select",
            props: {
              options: [
                { label: "已盖章", value: "1" },
                { label: "未盖章", value: "0" },
              ],
              optionLabel: "label",
              optionValue: "value",
              placeholder: "请选择我方盖章状态",
            },
          },
          {
            field: "returnComplete",
            title: "退回完成",
            element: "el-select",
            props: {
              options: [
                { label: "已完成", value: "1" },
                { label: "未完成", value: "0" },
              ],
              optionLabel: "label",
              optionValue: "value",
              placeholder: "请选择退回完成状态",
            },
          },
          {
            field: "remarks",
            title: "备注",
            element: "el-input",
            props: {
              type: "textarea",
              rows: 3,
              placeholder: "请输入备注",
            },
          },
          {
            field: "mode",
            title: "模式",
            element: "el-input",
            props: {
              placeholder: "请输入模式",
            },
          },
        ],
        crudPermission: [],
        customOperationTypes: [
          {
            title: "日志",
            typeName: "log",
            slotName: "log",
            showForm: false,
            event: (row) => {
              return this.handleLog(row);
            },
            condition: (row) => {
              return checkPermission(["xdtCharge:profitProgress:log"]);
            },
          },
          //非操作列
          {
            title: "批量操作",
            typeName: "batchOperation",
            slotName: "batchOperation",
            condition: (row) => {
              return false;
            },
          },
        ],
        formLayoutConfig: {
          defaultColSpan: 12,
          labelPosition: "right",
          labelWidth: "120px",
        },
      };
    },
  },
};
</script>

<style></style>
