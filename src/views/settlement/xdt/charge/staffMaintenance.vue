<!-- 结算人员与运营商维护 -->
<template>
  <div>
    <BuseCrud
      ref="crud"
      :loading="loading"
      :filterOptions="filterOptions"
      :tablePage="tablePage"
      :pagerProps="pagerProps"
      :tableColumn="tableColumn"
      :tableData="tableData"
      :tableProps="tableProps"
      :modalConfig="modalConfig"
      @modalConfirm="modalConfirmHandler"
      @loadData="loadData"
    >
      <template slot="filterCustomBtn">
        <div class="btn-wrap">
          <el-button
            type="primary"
            icon="el-icon-download"
            @click.stop="handleExport"
            v-has-permi="['xdtCharge:staffMaintenance:export']"
            >导出
          </el-button>
          <el-button
            type="primary"
            icon="el-icon-search"
            @click.stop="handleQuery"
            >查询
          </el-button>
          <el-button icon="el-icon-refresh" @click.stop="handleReset"
            >重置
          </el-button>
        </div>
      </template>
      <template #toolbar_buttons>
        <el-button
          type="primary"
          @click="handleBatchAdd"
          v-has-permi="['xdtCharge:staffMaintenance:add']"
          >新增</el-button
        >
        <el-button
          type="primary"
          @click="handleBatchImport"
          v-has-permi="['xdtCharge:staffMaintenance:import']"
          >导入</el-button
        >
      </template>
    </BuseCrud>
  </div>
</template>

<script>
import checkPermission from "@/utils/permission.js";
import api from "@/api/settlement/xdt/charge/staffMaintenance.js";
import { initParams } from "@/utils/buse.js";
import exportMixin from "@/mixin/export.js";

export default {
  name: "staffMaintenance",
  mixins: [exportMixin],
  data() {
    return {
      workLoading: false,
      tableTotal: 0,
      // 查询参数
      searchForm: {
        pageNum: 1,
        pageSize: 10,
      },
      visible: false,
      //buse参数-s
      tableProps: {
        border: true,
        align: "center",
        resizable: true,
        showOverflow: "tooltip",
        toolbarConfig: {
          custom: true,
          slots: {
            buttons: "toolbar_buttons",
          },
        },
        rowConfig: {
          keyField: "id",
          isCurrent: true,
        },
        checkboxConfig: {
          reserve: true,
        },
      },
      tableData: [],
      params: {},
      loading: false,
      pagerProps: {
        layouts: [
          "Total",
          "Sizes",
          "PrevPage",
          "JumpNumber",
          "NextPage",
          "FullJump",
        ],
        background: true,
        className: "pagination-container",
        pageSizes: [10, 20, 30, 50],
      },
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      },
      operationType: "accept",
      //buse参数-e

      // 下拉选项数据
      modeOptions: [],
      responsiblePersonOptions: [],
    };
  },
  created() {
    this.params = {
      ...initParams(this.filterOptions.config),
    };
    // 获取下拉列表数据
    this.loadDropListData();
  },
  methods: {
    checkPermission,

    handleBatchAdd() {
      this.$refs.crud.switchModalView(true, "add");
    },

    handleBatchImport() {
      // 导入功能实现
      console.log("导入功能");
    },

    // 加载下拉列表数据
    async loadDropListData() {
      try {
        const res = await api.getDropLists();
        if (res.success && res.data) {
          if (res.success) {
            // 处理下拉列表数据
            if (res.data.responsiblePerson) {
              this.responsiblePersonOptions = res.data.responsiblePerson.map(
                (item) => ({
                  dictLabel: item,
                  dictValue: item,
                })
              );
            }
            if (res.data.mode) {
              this.modeOptions = res.data.mode.map((item) => ({
                dictLabel: item,
                dictValue: item,
              }));
            }
          }
        }
      } catch (error) {
        console.error("获取下拉列表数据失败:", error);
      }
    },

    handleExport() {
      let params = {
        ...this.params,
      };
      this.handleCommonExport(api.export, params);
    },

    async loadData() {
      let params = {
        ...this.params,
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
      };

      this.loading = true;
      const res = await api.list(params);
      this.loading = false;
      this.tableData = res.data;
      this.tablePage.total = res.total;
    },

    handleReset() {
      this.tablePage = {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      };
      this.params = initParams(this.filterOptions.config);
      this.loadData();
    },

    handleQuery() {
      this.tablePage.currentPage = 1;
      this.loadData();
    },

    //弹窗确认按钮事件
    async modalConfirmHandler({ crudOperationType, ...formParams }) {
      let params = { ...formParams };
      console.log(crudOperationType, formParams, "提交");

      try {
        if (crudOperationType === "add") {
          await api.add(params);
          this.$message.success("新增成功");
        } else if (crudOperationType === "edit") {
          await api.update(params);
          this.$message.success("更新成功");
        }
        this.loadData();
      } catch (error) {
        console.error("操作失败:", error);
        return false;
      }
    },
  },
  computed: {
    tableColumn() {
      return [
        {
          field: "operator",
          title: "运营商",
        },
        {
          field: "mode",
          title: "模式",
        },
        {
          field: "reconciliationPerson",
          title: "负责人",
        },
        {
          field: "remarks",
          title: "备注",
        },
        {
          field: "updateBy",
          title: "操作人",
        },
        {
          field: "updateTime",
          title: "操作时间",
        },
      ];
    },
    filterOptions() {
      return {
        showCount: 4, //默认显示筛选项的个数 多余的展开展示
        layout: "right",
        inline: true,
        labelWidth: "140px",
        //筛选控件配置
        config: [
          {
            field: "operator",
            element: "el-input",
            title: "运营商",
          },

          {
            field: "mode",
            title: "模式",
            element: "el-select",
            props: {
              options: this.modeOptions,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
              filterable: true,
            },
          },
          {
            field: "responsiblePerson",
            element: "el-select",
            title: "负责人",
            props: {
              options: this.responsiblePersonOptions,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
              filterable: true,
            },
          },
        ],
        params: this.params,
      };
    },

    modalConfig() {
      return {
        viewBtn: true,
        submitBtn: true,
        okText: "确认",
        cancelText: "取消",
        addBtn: true,
        editBtn: true,
        delBtn: true,
        menu: true,
        menuWidth: 250,
        menuFixed: "right",
        modalWidth: "600px",
        formConfig: [
          {
            field: "operator",
            title: "运营商",
            element: "el-input",
            rules: [{ required: true, message: "请输入运营商名称" }],
            props: {
              placeholder: "请输入运营商名称",
            },
          },
          {
            field: "reconciliationPerson",
            title: "对账人",
            element: "el-input",
            rules: [{ required: true, message: "请输入对账人" }],
            props: {
              placeholder: "请输入对账人",
            },
          },
          {
            field: "contactInformation",
            title: "联系方式",
            element: "el-input",
            rules: [{ required: true, message: "请输入联系方式" }],
            props: {
              placeholder: "请输入联系方式",
            },
          },
          {
            field: "status",
            title: "状态",
            element: "el-select",
            props: {
              options: [
                { label: "正常", value: "0" },
                { label: "停用", value: "1" },
              ],
              optionLabel: "label",
              optionValue: "value",
              placeholder: "请选择状态",
            },
            defaultValue: "0",
          },
        ],
        crudPermission: [],
        customOperationTypes: [],
        formLayoutConfig: {
          defaultColSpan: 24,
          labelPosition: "right",
          labelWidth: "120px",
        },
      };
    },
  },
};
</script>

<style></style>
